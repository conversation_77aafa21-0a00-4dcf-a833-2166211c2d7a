{"name": "SW-App", "private": true, "version": "1.0.0", "description": "Sport Wrench application", "dependencies": {"@sailshq/upgrade": "^1.0.9", "angular": "^1.5.6", "angular-animate": "^1.3.20", "angular-bootstrap-colorpicker": "^3.0.32", "angular-clipboard": "^1.6.2", "angular-cookies": "^1.5.8", "angular-file-upload": "^2.3.3", "angular-loading-bar": "^0.7.1", "angular-resource": "^1.2.32", "angular-route": "^1.2.32", "angular-sanitize": "^1.2.32", "angular-simple-logger": "^0.1.7", "angular-toastr": "^1.5.0", "angular-ui-bootstrap": "^0.13.4", "angular-ui-router": "^0.3.1", "angularjs-dropdown-multiselect": "^2.0.0-beta.10", "apidoc": "^0.28.1", "apidoc-plugin-example": "^0.2.0", "bootstrap-sass": "^3.2.0", "bull": "^4.12.9", "ckeditor4": "^4.22.1", "co": "^4.6.0", "connect-redis": "^3.3.2", "cron": "^1.0.5", "date-utils": "^1.2.15", "dotenv": "^8.0.0", "ejs": "^3.1.10", "eventemitter2": "^0.4.14", "express": "^4.19.2", "express-xml-bodyparser": "^0.3.0", "fast-xml-parser": "^4.3.2", "font-awesome": "^4.7.0", "geopoint": "^1.0.1", "googlemaps": "^1.12.0", "grunt": "^1.5.3", "grunt-angular-templates": "^1.0.4", "grunt-autoprefixer": "^3.0.4", "grunt-bower-install-simple": "^1.0.0", "grunt-contrib-clean": "^2.0.1", "grunt-contrib-coffee": "^2.1.0", "grunt-contrib-compress": "^1.3.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-cssmin": "^4.0.0", "grunt-contrib-uglify": "^3.0.1", "grunt-contrib-watch": "^1.1.0", "grunt-filerev": "^2.3.1", "grunt-usemin": "^3.1.1", "hashids": "^1.0.2", "html-minifier": "^2.1.0", "html-pdf": "^3.0.1", "htmlparser2": "^4.1.0", "include-all": "^4.0.3", "joi": "^17.2.0", "jquery": "^2.2.0", "js-xlsx": "^0.8.22", "json-gate": "^0.8.21", "json2xls": "0.0.5", "knex": "^3.1.0", "knox": "^0.9.2", "lodash": "^4.17.21", "moment": "^2.24.0", "moment-timezone": "^0.5.23", "mz": "^2.4.0", "ng-infinite-scroll": "^1.3.0", "ng-table": "^0.5.4", "ngstorage": "^0.3.11", "node-fetch": "^1.7.3", "nodemon": "^1.2.0", "nodexml": "^1.0.2", "numeral": "^2.0.6", "oclazyload": "^1.1.0", "optimist": "0.3.4", "passbook": "^2.1.1", "passport": "^0.2.0", "passport-http": "^0.3.0", "passport-local": "^1.0.0", "passport-remember-me": "0.0.1", "pg": "^8.11.5", "pg-connection-string": "^2.6.4", "pg-cursor": "^2.10.5", "pg-pool": "^3.6.2", "phantom": "^6.0.3", "plaid": "^2.0.5", "pmx": "^1.6.7", "qr-image": "^3.1.0", "qs": "^6.13.1", "request": "^2.34.0", "request-promise": "^4.1.1", "route-parser": "0.0.5", "sails": "^1.5.11", "sails-hook-sockets": "^1.5.5", "sails-routes-swagger": "^1.1.1", "sharp": "^0.32.6", "sinon": "^7.3.2", "skipper-s3": "^0.6.0", "squel": "5.10.0", "string-format": "^0.5.0", "stripe": "^14.15.0", "twilio": "^3.7.0", "ui-select": "^0.19.8", "underscore": "^1.8.3", "winston": "^0.7.3", "winston-graylog2": "^1.1.0", "winston-syslog": "^1.2.6", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.2/xlsx-0.20.2.tgz", "xlsx-style": "^0.8.13", "xml": "^1.0.1", "xss-escape": "0.0.5", "yargs": "^13.3.0"}, "scripts": {"start": "node app.js --dev --mult=\"/tickets\"", "debug": "node debug app.js --dev --mult=\"/tickets\"", "scheduler": "NODE_ENV=\"production\" node scheduler/scheduler.worker --prod || exit 0", "scheduler-dev": "NODE_ENV=\"development\" node scheduler/scheduler.worker --dev || exit 0", "test": "mocha --timeout 30000", "migrate-main": "knex --cwd db  --migrations-directory ./migrations/main --connection $SW_DB migrate:latest --client postgresql", "migrate-email": "knex --cwd db  --migrations-directory ./migrations/emailqueue --connection $EMAIL_DB migrate:latest --client postgresql", "migrate-test": "knex --cwd db  --migrations-directory ./migrations/emailqueue --connection $TEST_DB_CONNECTION migrate:latest --client postgresql", "migration-main:create": "knex --cwd db  --migrations-directory ./migrations/main migrate:make", "doc": "apidoc -i ./userconfig/routes -o ./.tmp/doc/", "start:frontend:grunt": "grunt", "start:frontend:events:grunt": "grunt esw", "start:frontend:admin:grunt": "grunt asw", "start:worker-queue": "node worker/main.js --dev", "start:frontend": "webpack serve --config webpack.config.frontend.js", "start:frontend:events": "webpack serve --config webpack.config.frontend_event.js", "start:frontend:admin": "webpack serve --config webpack.config.frontend_admin.js", "build:frontend": "webpack --mode production --config webpack.config.frontend.js", "build:event": "webpack --mode production --config webpack.config.frontend_event.js", "build:admin": "webpack --mode production --config webpack.config.frontend_admin.js"}, "main": "app.js", "repository": "", "author": "SportWrench", "license": "", "homepage": "sportwrench.com", "devDependencies": {"@babel/core": "^7.24.7", "@babel/preset-env": "^7.24.7", "axios": "^1.7.2", "babel-loader": "^9.1.3", "babel-plugin-angularjs-annotate": "^0.10.0", "babel-polyfill": "^6.23.0", "babel-preset-env": "^1.7.0", "babel-runtime": "^6.23.0", "bower": "^1.8.14", "chai": "^3.5.0", "chai-as-promised": "^6.0.0", "clean-webpack-plugin": "^4.0.0", "cookie": "^0.3.1", "copy-webpack-plugin": "^12.0.2", "css-loader": "^7.1.2", "glob": "^11.0.0", "grunt-babel": "^6.0.0", "grunt-browser-sync": "^2.2.0", "grunt-ng-annotate": "^4.0.0", "grunt-ng-constant": "^2.0.1", "grunt-preprocess": "^5.1.0", "grunt-purifycss": "^0.1.1", "grunt-sass": "^3.0.2", "grunt-sync": "^0.8.1", "html-loader": "^5.0.0", "html-webpack-plugin": "^5.6.3", "http-proxy-middleware": "^0.17.3", "inquirer": "^7.1.0", "mini-css-extract-plugin": "^2.9.0", "mocha": "^3.0.1", "nock": "^8.0.0", "sass": "^1.77.7", "sass-loader": "^14.2.1", "should": "^10.0.0", "terser-webpack-plugin": "^5.3.10", "underscore.string": "^2.3.3", "webpack": "^5.92.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.4"}}