const path = require('node:path');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const glob = require('glob');

const entryFiles = glob.sync([
    './frontend/sport-wrench.module.js',
    './frontend/**/*.js',
    './assets/styles/main.scss',
    './assets/styles/admin.scss',
], { absolute: true });

const vendorEntry = './vendor.js';

module.exports = {
    extends: path.resolve(__dirname, './webpack.config.base.js'),

    entry: {
        main: entryFiles,
        vendor: vendorEntry,
    },

    plugins: [
        {
            apply: (compiler) => {
                new CopyWebpackPlugin({
                    patterns: [
                        { context: './frontend/', from: '**/*.html', to: '[path][name][ext]' },
                    ],
                }).apply(compiler);
            }
        }
    ],

    devServer: {
        port: 8079,
    },
};
