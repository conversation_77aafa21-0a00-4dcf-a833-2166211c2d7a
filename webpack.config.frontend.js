const path = require('node:path');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const glob = require('glob');

module.exports = {
    extends: path.resolve(__dirname, './webpack.config.base.js'),

    entry: './frontend/index.js',

    plugins: [
        new HtmlWebpackPlugin({
            template: './frontend/index.html',
            filename: 'index.html',
            inject: 'body',
        }),
        {
            apply: (compiler) => {
                new CopyWebpackPlugin({
                    patterns: [
                        { context: './frontend/', from: '**/*.html', to: '[path][name][ext]', globOptions: { ignore: ['**/index.html'] } },
                    ],
                }).apply(compiler);
            }
        }
    ],

    devServer: {
        port: 8079,
    },
};
