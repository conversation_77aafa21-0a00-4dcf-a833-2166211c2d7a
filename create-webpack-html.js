#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Read the template
const templatePath = './frontend/index-webpack.html';
const outputPath = './.tmp/public/index.html';
const publicDir = './.tmp/public';

// Get all generated bundle files
const files = fs.readdirSync(publicDir);
const bundles = {
    vendorCss: files.filter(f => f.startsWith('vendor-libs.') && f.endsWith('.css'))[0],
    angularCss: files.filter(f => f.startsWith('angular-vendor.') && f.endsWith('.css'))[0],
    mainCss: files.filter(f => f.startsWith('main.') && f.endsWith('.css'))[0],
    vendorLibsJs: files.filter(f => f.startsWith('vendor-libs.') && f.endsWith('.js'))[0],
    angularJs: files.filter(f => f.startsWith('angular-vendor.') && f.endsWith('.js'))[0],
    vendorJs: files.filter(f => f.startsWith('vendor.') && f.endsWith('.js'))[0],
    mainJs: files.filter(f => f.startsWith('main.') && f.endsWith('.js'))[0]
};

console.log('Found bundles:', bundles);

// Read template
let html = fs.readFileSync(templatePath, 'utf8');

// Replace CSS injection point
const cssLinks = [
    bundles.vendorCss ? `    <link rel="stylesheet" href="${bundles.vendorCss}">` : '',
    bundles.angularCss ? `    <link rel="stylesheet" href="${bundles.angularCss}">` : '',
    bundles.mainCss ? `    <link rel="stylesheet" href="${bundles.mainCss}">` : ''
].filter(Boolean).join('\n');

html = html.replace(
    '    <!-- Webpack will inject vendor CSS and main CSS here -->',
    cssLinks
);

// Replace JS injection point
const jsScripts = [
    bundles.vendorLibsJs ? `    <script src="${bundles.vendorLibsJs}"></script>` : '',
    bundles.angularJs ? `    <script src="${bundles.angularJs}"></script>` : '',
    bundles.vendorJs ? `    <script src="${bundles.vendorJs}"></script>` : '',
    bundles.mainJs ? `    <script src="${bundles.mainJs}"></script>` : ''
].filter(Boolean).join('\n');

html = html.replace(
    '    <!-- Webpack will inject vendor bundle and main bundle here -->',
    jsScripts
);

// Write the final HTML
fs.writeFileSync(outputPath, html);
console.log(`Generated ${outputPath} with hashed bundles`);
