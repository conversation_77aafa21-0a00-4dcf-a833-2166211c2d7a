/**
 * Main entry point for the frontend application
 * Following AngularJS + WebPack best practices
 */

// Import vendor dependencies first
import './vendor.js';

// Import global styles
import '../assets/styles/main.scss';
import '../assets/styles/admin.scss';

// Import the main application module
import './sport-wrench.module.js';

// Import all application modules recursively
function requireAll(r) {
    r.keys().forEach(r);
}

// Import all JS files in frontend directory (excluding index.js and vendor.js)
requireAll(require.context('./', true, /^(?!.*(index|vendor)\.js$).*\.js$/));
