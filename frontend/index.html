<!doctype html>
<html class="no-js" xmlns="http://www.w3.org/1999/html" ng-app="SportWrench">
<head>
    <meta charset="utf-8"/>
    <title>SportWrench - The Home of Sporting Events</title>
    <meta name="description" content="SportWrench - The Home of Sporting Events"/>
    <meta name="viewport" content="width=device-width"/>
    <link rel="stylesheet" href="../assets/bower_components/angular-loading-bar/build/loading-bar.min.css" />
    <link rel="stylesheet" href="../assets/bower_components/angular/angular-csp.css" />
    <link rel="stylesheet" href="../assets/bower_components/font-awesome/css/font-awesome.css" />
    <link rel="stylesheet" href="../assets/bower_components/ng-table/dist/ng-table.css" />
    <link rel="stylesheet" href="../assets/bower_components/angular-toastr/dist/angular-toastr.css" />
    <link rel="stylesheet" href="../assets/bower_components/angular-ui-select/dist/select.css" />
    <link rel="stylesheet" href="../assets/bower_components/angular-bootstrap-colorpicker/css/colorpicker.min.css" />
<!--    <link rel="stylesheet" href="main.css"/>-->
</head>
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-6F50QB760L"></script>
<script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-6F50QB760L', { 'debug_mode':true, 'send_page_view': false });
</script>
<body ng-controller="MainController" ng-cloak>
    <!--[if lte IE 9]>
      <p class="text-center">You are using an <strong class="text-danger">outdated</strong> browser. Please <a href="http://browsehappy.com/">upgrade your browser</a> to use SportWrench</p>
    <![endif]-->
    <div class="navbar navbar-default navbar--m0">
        <div class="container" ng-init="opts.isCollapsed = !opts.isCollapsed">
            <div class="navbar-header">                
                <a class="navbar-brand main-log-navbar {{logoVisibility()}}" ng-href="{{homePageUrl}}">
                    <img src="../assets/images/sw_main_logo.svg" style="width: 115px; height: 50px;">
                </a>                
                <button class="mobile-menu navbar-toggle pull-right" ng-click="opts.isCollapsed = !opts.isCollapsed">
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
            </div>
            <nav uib-collapse="opts.isCollapsed" class="navbar-collapse navbar-ex1-collapse">
                <ul class="nav navbar-nav navbar-right" ng-if="!isLoggedIn()" ng-click="closeMenu()">
                    <li uib-dropdown ng-click="$event.stopPropagation()">
                        <a href="" uib-dropdown-toggle>Support <span class="caret"></span></a>
                        <ul class="dropdown-menu" ng-click="closeMenu()">
                            <li><a href="" ui-state="routes.faq">FAQ</a></li>
                            <li><a href="" ui-state="routes.ask">Ask Question</a></li>
                        </ul>
                    </li>
                </ul>
                <ul class="nav navbar-nav navbar-right" ng-if="isLoggedIn();" ng-click="closeMenu()">
                    <li ng-if="isUSAVAdmin()">
                        <a ui-state="routes.ua">USAV</a>
                    </li>
                    <li ng-if="isMyEventsVisible()">
                        <a ui-state="routes.events">My Events</a>
                    </li>
                    <li ng-if="hasGodRole()">
                        <a ui-state="routes.es">Event Supervisor</a>
                    </li>
                    <li ng-if="isClubDirector()">
                        <a ui-state="routes.club">My Сlub</a>
                    </li>
                    <li ng-if="isSales() || hasGodRole()">
                        <a ui-state="routes.sales">Sales</a>
                    </li>
                    <li ng-if="isSponsor()">
                        <a ui-state="routes.profile">Exhibitor</a>
                    </li>
                    <li ng-if="isOfficial()">
                        <a ui-state="routes.official">Staff / Official</a>
                    </li>
                    <li ng-if="isHousingManager()">
                        <a ui-state="routes.housing">Housing</a>
                    </li>
                    <li ng-if="hasTickets()">
                        <a href="/spectator/tickets">My Tickets</a>
                    </li>
                    <li uib-dropdown ng-click="$event.stopPropagation()">
                        <a href="" uib-dropdown-toggle>Support <span class="caret"></span></a>
                        <ul class="dropdown-menu" ng-click="closeMenu()">
                            <li><a href="" ui-state="routes.faq">FAQ</a></li>
                            <li><a href="" ui-state="routes.ask">Ask Question</a></li>
                        </ul>
                    </li>
                    <li>
                        <a>Hello, <span class="text-link pointer" ui-sref="account">{{userfirst}}</span></a>
                    </li>
                    <li>
                        <a href="" ng-click="logOut()">Logout</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <div class="container-fluid block-height-min" ui-view="rootView" autoscroll="true"></div>
        
    <footer class="container-fluid footer">
        <div class="container ">
            <div class="pull-left godaddy">
                <span id="siteseal">
                    <script
                        async
                        type="text/javascript"
                        src="https://seal.godaddy.com/getSeal?sealID=6VPo2xbfSPYZZTl2ZEtKt51WLNgGUOANn350LWkrCboP4a1DrNyGweVEUA2K">
                    </script>
                </span>
            </div>  
            <div class="pull-left card-types">
                <img delayed-load="images/types.png" alt="accepted card types">
            </div>          
            <div class="copyright pull-left">&copy; SportWrench Inc. {{year}}. All rights reserved</div>
            <div class="pull-right">
                <ul class="nav navbar-nav terms">
                    <li><a ui-state="routes.terms">Terms</a></li>
                    <li><a ui-state="routes.privacy">Privacy</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script src="../assets/js/polyfills/babel-polyfill.js"></script>
    <script src="../assets/js/polyfills/array-includes.js"></script>
    <script src="../assets/js/polyfills/string-includes.js"></script>
    <script src="../assets/bower_components/jquery/jquery.min.js"></script>
    <script src="../assets/bower_components/underscore/underscore-min.js"></script>
    <script src="../assets/bower_components/angular/angular.min.js"></script>
    <script src="../assets/bower_components/angular-sanitize/angular-sanitize.min.js"></script>
    <script src="../assets/bower_components/angular-ui-router/release/angular-ui-router.min.js"></script>
    <script src="../assets/bower_components/ngstorage/ngStorage.min.js"></script>
    <script src="../assets/js/uib-custom/ui-bootstrap-custom-tpls-1.3.3.min.js"></script>
    <script src="../assets/bower_components/moment/min/moment.min.js"></script>
    <script src="../assets/bower_components/angular-ui-utils/mask.min.js"></script>
    <script src="../assets/bower_components/oclazyload/dist/ocLazyLoad.min.js"></script>
    <script src="../assets/bower_components/angular-loading-bar/build/loading-bar.min.js"></script>
    <script src="../assets/bower_components/StickyTableHeaders/js/jquery.stickytableheaders.min.js"></script>
    <script src="../assets/bower_components/ng-table/dist/ng-table.min.js"></script>
    <script src="../assets/bower_components/angular-clipboard/angular-clipboard.js"></script>
    <script src="../assets/bower_components/ngInfiniteScroll/build/ng-infinite-scroll.min.js"></script>
    <script src="../assets/bower_components/bootstrap-ui-datetime-picker/dist/datetime-picker.js"></script><!-- Probably we can load it only on the needed pages, or only for event owners (this is used only in the dashboard) -->
    <script src="../assets/bower_components/angular-animate/angular-animate.min.js"></script> <!-- Is this in use? -->
    <script src="../assets/bower_components/angular-toastr/dist/angular-toastr.tpls.min.js"></script>
    <script src="../assets/bower_components/angular-ui-select/dist/select.js"></script>
    <script src="../assets/bower_components/angular-bootstrap-colorpicker/js/bootstrap-colorpicker-module.min.js"></script>
<!--    <script src="../assets/bower_components/angularjs-dropdown-multiselect/dist/angularjs-dropdown-multiselect.min.js" type="text/javascript"></script>-->
    <script src="../assets/js/signature_pad/signature_pad.js"></script>
    <script src="../assets/bower_components/angular-simple-logger/dist/angular-simple-logger.min.js"></script>

    <script src="index.js"></script>
    <!-- BEGIN SENTRY -->
    <script
        src="https://browser.sentry-cdn.com/7.109.0/bundle.tracing.replay.min.js"
        integrity="sha384-yUeLF1YOBa+gnOMSLMiMI/kkW40NC9CjlKj9I8sVm98HyWD6s57bVosZwg+Hp8Cb"
        crossorigin="anonymous"
    ></script>

    <!-- If you include the integration it will be available under Sentry.Integrations.Angular -->
    <script
        src="https://browser.sentry-cdn.com/6.19.7/angular.min.js"
        crossorigin="anonymous"
    ></script>

    <script>
        Sentry.init({
            dsn: "https://<EMAIL>/4",
            beforeSend: function(event) {
                // filter out UnhandledRejection errors that have no information
                if (event && event.exception && event.exception.values && event.exception.values.length) {
                    const {type, value} = event.exception.values[0];

                    const nonErrorPromiseRejectionType = 'UnhandledRejection';
                    const nonErrorPromiseRejectionValue = 'Non-Error promise rejection captured';

                    if (type === nonErrorPromiseRejectionType && value.includes(nonErrorPromiseRejectionValue)) {
                        return null;
                    }
                }
            },
            integrations: [
                new Sentry.Integrations.Angular(),
                new Sentry.BrowserTracing(),
                new Sentry.Replay({
                    maskAllText: false,
                    blockAllMedia: false,
                })
            ],
            ignoreErrors: [
                /captcha/i, /timeout/i, /localStorage/i, /unauthorized/i,
                /modulerr/i, /validation/i, /bower/i, /__gCrWeb/i,
                /Non-Error promise rejection captured/i,
                /Object captured as exception/i,
                /Object captured as promise/i,
            ],
            ignoreUrls: [
                /^chrome(-extension)?:\/\//i,
            ],
            tracesSampleRate: 1.0,
            replaysSessionSampleRate: 0,
            replaysOnErrorSampleRate: 0.5
        });
    </script>
    <!-- END SENTRY -->
</body>
</html>
